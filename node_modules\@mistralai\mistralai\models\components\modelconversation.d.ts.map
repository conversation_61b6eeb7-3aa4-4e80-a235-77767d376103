{"version": 3, "file": "modelconversation.d.ts", "sourceRoot": "", "sources": ["../../src/models/components/modelconversation.ts"], "names": [], "mappings": "AAIA,OAAO,KAAK,CAAC,MAAM,KAAK,CAAC;AAGzB,OAAO,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAClD,OAAO,EAAE,MAAM,IAAI,eAAe,EAAE,MAAM,mBAAmB,CAAC;AAC9D,OAAO,EAAE,kBAAkB,EAAE,MAAM,iCAAiC,CAAC;AACrE,OAAO,EACL,mBAAmB,EAEnB,4BAA4B,EAE7B,MAAM,0BAA0B,CAAC;AAClC,OAAO,EACL,cAAc,EAEd,uBAAuB,EAExB,MAAM,qBAAqB,CAAC;AAC7B,OAAO,EACL,mBAAmB,EAEnB,4BAA4B,EAE7B,MAAM,0BAA0B,CAAC;AAClC,OAAO,EACL,YAAY,EAEZ,qBAAqB,EAEtB,MAAM,mBAAmB,CAAC;AAC3B,OAAO,EACL,mBAAmB,EAEnB,4BAA4B,EAE7B,MAAM,0BAA0B,CAAC;AAClC,OAAO,EACL,oBAAoB,EAEpB,6BAA6B,EAE9B,MAAM,2BAA2B,CAAC;AACnC,OAAO,EACL,aAAa,EAEb,sBAAsB,EAEvB,MAAM,oBAAoB,CAAC;AAE5B,MAAM,MAAM,sBAAsB,GAC9B,CAAC,mBAAmB,GAAG;IAAE,IAAI,EAAE,kBAAkB,CAAA;CAAE,CAAC,GACpD,CAAC,mBAAmB,GAAG;IAAE,IAAI,EAAE,kBAAkB,CAAA;CAAE,CAAC,GACpD,CAAC,aAAa,GAAG;IAAE,IAAI,EAAE,YAAY,CAAA;CAAE,CAAC,GACxC,CAAC,oBAAoB,GAAG;IAAE,IAAI,EAAE,oBAAoB,CAAA;CAAE,CAAC,GACvD,CAAC,mBAAmB,GAAG;IAAE,IAAI,EAAE,kBAAkB,CAAA;CAAE,CAAC,GACpD,CAAC,YAAY,GAAG;IAAE,IAAI,EAAE,UAAU,CAAA;CAAE,CAAC,CAAC;AAE1C,eAAO,MAAM,uBAAuB;;CAE1B,CAAC;AACX,MAAM,MAAM,uBAAuB,GAAG,UAAU,CAC9C,OAAO,uBAAuB,CAC/B,CAAC;AAEF,MAAM,MAAM,iBAAiB,GAAG;IAC9B;;OAEG;IACH,YAAY,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;IACzC;;OAEG;IACH,KAAK,CAAC,EACF,KAAK,CACH,CAAC,mBAAmB,GAAG;QAAE,IAAI,EAAE,kBAAkB,CAAA;KAAE,CAAC,GACpD,CAAC,mBAAmB,GAAG;QAAE,IAAI,EAAE,kBAAkB,CAAA;KAAE,CAAC,GACpD,CAAC,aAAa,GAAG;QAAE,IAAI,EAAE,YAAY,CAAA;KAAE,CAAC,GACxC,CAAC,oBAAoB,GAAG;QAAE,IAAI,EAAE,oBAAoB,CAAA;KAAE,CAAC,GACvD,CAAC,mBAAmB,GAAG;QAAE,IAAI,EAAE,kBAAkB,CAAA;KAAE,CAAC,GACpD,CAAC,YAAY,GAAG;QAAE,IAAI,EAAE,UAAU,CAAA;KAAE,CAAC,CACxC,GACC,SAAS,CAAC;IACd;;OAEG;IACH,cAAc,CAAC,EAAE,cAAc,GAAG,SAAS,CAAC;IAC5C;;OAEG;IACH,IAAI,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;IACjC;;OAEG;IACH,WAAW,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;IACxC,MAAM,CAAC,EAAE,uBAAuB,GAAG,SAAS,CAAC;IAC7C,EAAE,EAAE,MAAM,CAAC;IACX,SAAS,EAAE,IAAI,CAAC;IAChB,SAAS,EAAE,IAAI,CAAC;IAChB,KAAK,EAAE,MAAM,CAAC;CACf,CAAC;AAEF,gBAAgB;AAChB,eAAO,MAAM,oCAAoC,EAAE,CAAC,CAAC,OAAO,CAC1D,sBAAsB,EACtB,CAAC,CAAC,UAAU,EACZ,OAAO,CAgCP,CAAC;AAEH,gBAAgB;AAChB,MAAM,MAAM,+BAA+B,GACvC,CAAC,4BAA4B,GAAG;IAAE,IAAI,EAAE,kBAAkB,CAAA;CAAE,CAAC,GAC7D,CAAC,4BAA4B,GAAG;IAAE,IAAI,EAAE,kBAAkB,CAAA;CAAE,CAAC,GAC7D,CAAC,sBAAsB,GAAG;IAAE,IAAI,EAAE,YAAY,CAAA;CAAE,CAAC,GACjD,CAAC,6BAA6B,GAAG;IAAE,IAAI,EAAE,oBAAoB,CAAA;CAAE,CAAC,GAChE,CAAC,4BAA4B,GAAG;IAAE,IAAI,EAAE,kBAAkB,CAAA;CAAE,CAAC,GAC7D,CAAC,qBAAqB,GAAG;IAAE,IAAI,EAAE,UAAU,CAAA;CAAE,CAAC,CAAC;AAEnD,gBAAgB;AAChB,eAAO,MAAM,qCAAqC,EAAE,CAAC,CAAC,OAAO,CAC3D,+BAA+B,EAC/B,CAAC,CAAC,UAAU,EACZ,sBAAsB,CAgCtB,CAAC;AAEH;;;GAGG;AACH,yBAAiB,uBAAuB,CAAC;IACvC,sEAAsE;IAC/D,MAAM,aAAa,0DAAuC,CAAC;IAClE,uEAAuE;IAChE,MAAM,cAAc,kFAAwC,CAAC;IACpE,iEAAiE;IACjE,KAAY,QAAQ,GAAG,+BAA+B,CAAC;CACxD;AAED,wBAAgB,4BAA4B,CAC1C,sBAAsB,EAAE,sBAAsB,GAC7C,MAAM,CAIR;AAED,wBAAgB,8BAA8B,CAC5C,UAAU,EAAE,MAAM,GACjB,eAAe,CAAC,sBAAsB,EAAE,kBAAkB,CAAC,CAM7D;AAED,gBAAgB;AAChB,eAAO,MAAM,qCAAqC,EAAE,CAAC,CAAC,aAAa,CACjE,OAAO,uBAAuB,CACS,CAAC;AAE1C,gBAAgB;AAChB,eAAO,MAAM,sCAAsC,EAAE,CAAC,CAAC,aAAa,CAClE,OAAO,uBAAuB,CACS,CAAC;AAE1C;;;GAGG;AACH,yBAAiB,wBAAwB,CAAC;IACxC,uEAAuE;IAChE,MAAM,aAAa;;MAAwC,CAAC;IACnE,wEAAwE;IACjE,MAAM,cAAc;;MAAyC,CAAC;CACtE;AAED,gBAAgB;AAChB,eAAO,MAAM,+BAA+B,EAAE,CAAC,CAAC,OAAO,CACrD,iBAAiB,EACjB,CAAC,CAAC,UAAU,EACZ,OAAO,CAmDP,CAAC;AAEH,gBAAgB;AAChB,MAAM,MAAM,0BAA0B,GAAG;IACvC,YAAY,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;IACzC,KAAK,CAAC,EACF,KAAK,CACH,CAAC,4BAA4B,GAAG;QAAE,IAAI,EAAE,kBAAkB,CAAA;KAAE,CAAC,GAC7D,CAAC,4BAA4B,GAAG;QAAE,IAAI,EAAE,kBAAkB,CAAA;KAAE,CAAC,GAC7D,CAAC,sBAAsB,GAAG;QAAE,IAAI,EAAE,YAAY,CAAA;KAAE,CAAC,GACjD,CAAC,6BAA6B,GAAG;QAAE,IAAI,EAAE,oBAAoB,CAAA;KAAE,CAAC,GAChE,CAAC,4BAA4B,GAAG;QAAE,IAAI,EAAE,kBAAkB,CAAA;KAAE,CAAC,GAC7D,CAAC,qBAAqB,GAAG;QAAE,IAAI,EAAE,UAAU,CAAA;KAAE,CAAC,CACjD,GACC,SAAS,CAAC;IACd,eAAe,CAAC,EAAE,uBAAuB,GAAG,SAAS,CAAC;IACtD,IAAI,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;IACjC,WAAW,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;IACxC,MAAM,EAAE,MAAM,CAAC;IACf,EAAE,EAAE,MAAM,CAAC;IACX,UAAU,EAAE,MAAM,CAAC;IACnB,UAAU,EAAE,MAAM,CAAC;IACnB,KAAK,EAAE,MAAM,CAAC;CACf,CAAC;AAEF,gBAAgB;AAChB,eAAO,MAAM,gCAAgC,EAAE,CAAC,CAAC,OAAO,CACtD,0BAA0B,EAC1B,CAAC,CAAC,UAAU,EACZ,iBAAiB,CAmDjB,CAAC;AAEH;;;GAGG;AACH,yBAAiB,kBAAkB,CAAC;IAClC,iEAAiE;IAC1D,MAAM,aAAa,qDAAkC,CAAC;IAC7D,kEAAkE;IAC3D,MAAM,cAAc,wEAAmC,CAAC;IAC/D,4DAA4D;IAC5D,KAAY,QAAQ,GAAG,0BAA0B,CAAC;CACnD;AAED,wBAAgB,uBAAuB,CACrC,iBAAiB,EAAE,iBAAiB,GACnC,MAAM,CAIR;AAED,wBAAgB,yBAAyB,CACvC,UAAU,EAAE,MAAM,GACjB,eAAe,CAAC,iBAAiB,EAAE,kBAAkB,CAAC,CAMxD"}