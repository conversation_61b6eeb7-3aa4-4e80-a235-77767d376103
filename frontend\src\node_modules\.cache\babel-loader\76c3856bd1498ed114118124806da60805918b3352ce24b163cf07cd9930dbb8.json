{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Development\\\\Chatbot-Template\\\\trae\\\\multi-ai-chatbot\\\\frontend\\\\src\\\\src\\\\index.js\";\nimport React from 'react';\nimport ReactDOM from 'react-dom';\nimport './index.css';\nimport App from './App';\nimport reportWebVitals from './reportWebVitals';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nReactDOM.render(/*#__PURE__*/_jsxDEV(React.StrictMode, {\n  children: /*#__PURE__*/_jsxDEV(App, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 9,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 8,\n  columnNumber: 3\n}, this), document.getElementById('root'));\n\n// If you want to start measuring performance in your app, pass a function\n// to log results (for example: reportWebVitals(console.log))\n// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals\nreportWebVitals();", "map": {"version": 3, "names": ["React", "ReactDOM", "App", "reportWebVitals", "jsxDEV", "_jsxDEV", "render", "StrictMode", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "document", "getElementById"], "sources": ["C:/Users/<USER>/Development/Chatbot-Template/trae/multi-ai-chatbot/frontend/src/src/index.js"], "sourcesContent": ["import React from 'react';\nimport ReactDOM from 'react-dom';\nimport './index.css';\nimport App from './App';\nimport reportWebVitals from './reportWebVitals';\n\nReactDOM.render(\n  <React.StrictMode>\n    <App />\n  </React.StrictMode>,\n  document.getElementById('root')\n);\n\n// If you want to start measuring performance in your app, pass a function\n// to log results (for example: reportWebVitals(console.log))\n// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals\nreportWebVitals();\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,QAAQ,MAAM,WAAW;AAChC,OAAO,aAAa;AACpB,OAAOC,GAAG,MAAM,OAAO;AACvB,OAAOC,eAAe,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhDJ,QAAQ,CAACK,MAAM,cACbD,OAAA,CAACL,KAAK,CAACO,UAAU;EAAAC,QAAA,eACfH,OAAA,CAACH,GAAG;IAAAO,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACS,CAAC,EACnBC,QAAQ,CAACC,cAAc,CAAC,MAAM,CAChC,CAAC;;AAED;AACA;AACA;AACAX,eAAe,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}