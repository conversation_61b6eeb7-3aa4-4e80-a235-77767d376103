{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Development\\\\Chatbot-Template\\\\trae\\\\multi-ai-chatbot\\\\frontend\\\\src\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport './App.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [input, setInput] = useState('');\n  const [provider, setProvider] = useState('mistral');\n  const [model, setModel] = useState(''); // New state for model selection\n  const [messages, setMessages] = useState([]);\n  const [response, setResponse] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const messagesEndRef = useRef(null);\n  const scrollToBottom = () => {\n    var _messagesEndRef$curre;\n    (_messagesEndRef$curre = messagesEndRef.current) === null || _messagesEndRef$curre === void 0 ? void 0 : _messagesEndRef$curre.scrollIntoView({\n      behavior: \"smooth\"\n    });\n  };\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n  const handleSend = async () => {\n    if (!input.trim()) return;\n    const newMessages = [...messages, {\n      role: 'user',\n      content: input\n    }];\n    setMessages(newMessages);\n    setInput('');\n    setLoading(true);\n    setResponse(null);\n    try {\n      var _data$result, _data$result$choices, _data$result$choices$, _data$result$choices$2;\n      const requestBody = {\n        messages: newMessages,\n        provider\n      };\n      if (model) {\n        requestBody.model = model; // Add model to request body if selected\n      }\n      const res = await fetch('http://localhost:3001/chat', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(requestBody)\n      });\n      const data = await res.json();\n      setResponse(data.result);\n      setMessages([...newMessages, {\n        role: 'assistant',\n        content: ((_data$result = data.result) === null || _data$result === void 0 ? void 0 : (_data$result$choices = _data$result.choices) === null || _data$result$choices === void 0 ? void 0 : (_data$result$choices$ = _data$result$choices[0]) === null || _data$result$choices$ === void 0 ? void 0 : (_data$result$choices$2 = _data$result$choices$.message) === null || _data$result$choices$2 === void 0 ? void 0 : _data$result$choices$2.content) || data.result\n      }]);\n    } catch (err) {\n      setResponse('Error: ' + err.message);\n    }\n    setInput('');\n    setLoading(false);\n  };\n  const handleProviderChange = e => {\n    const selectedProvider = e.target.value;\n    setProvider(selectedProvider);\n    // Set default model based on provider, or clear if not applicable\n    switch (selectedProvider) {\n      case 'mistral':\n        setModel('mistral-large-latest');\n        break;\n      case 'openrouter':\n        setModel('deepseek/deepseek-r1-0528:free'); // Default for OpenRouter\n        break;\n      case 'gemini':\n        setModel('gemini-2.0-flash'); // Default for Gemini\n        break;\n      default:\n        setModel('');\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"App\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"chat-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"chat-header\",\n        children: /*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"AI Chatbot\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"provider-select-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"provider-select\",\n          children: \"Choose a provider:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          id: \"provider-select\",\n          value: provider,\n          onChange: handleProviderChange,\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"mistral\",\n            children: \"Mistral\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 76,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"openrouter\",\n            children: \"OpenRouter\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 77,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"gemini\",\n            children: \"Gemini\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 78,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 11\n        }, this), (provider === 'openrouter' || provider === 'gemini') && /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          placeholder: \"Enter model (e.g., gpt-4, gemini-pro)\",\n          value: model,\n          onChange: e => setModel(e.target.value)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"messages-container\",\n        children: [messages.map((msg, i) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `message ${msg.role}`,\n          children: msg.content\n        }, i, false, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 13\n        }, this)), loading && /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"loading-indicator\",\n          children: \"Loading...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 23\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          ref: messagesEndRef\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 89,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"input-area\",\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          value: input,\n          onChange: e => setInput(e.target.value),\n          onKeyDown: e => e.key === 'Enter' && handleSend(),\n          placeholder: \"Type your message...\",\n          disabled: loading\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleSend,\n          disabled: loading,\n          children: \"Send\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 98,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 69,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 68,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"UhQndKmXSvkhydpDH9d6sHVyNts=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "jsxDEV", "_jsxDEV", "App", "_s", "input", "setInput", "provider", "<PERSON><PERSON><PERSON><PERSON>", "model", "setModel", "messages", "setMessages", "response", "setResponse", "loading", "setLoading", "messagesEndRef", "scrollToBottom", "_messagesEndRef$curre", "current", "scrollIntoView", "behavior", "handleSend", "trim", "newMessages", "role", "content", "_data$result", "_data$result$choices", "_data$result$choices$", "_data$result$choices$2", "requestBody", "res", "fetch", "method", "headers", "body", "JSON", "stringify", "data", "json", "result", "choices", "message", "err", "handleProviderChange", "e", "<PERSON><PERSON><PERSON><PERSON>", "target", "value", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "htmlFor", "id", "onChange", "type", "placeholder", "map", "msg", "i", "ref", "onKeyDown", "key", "disabled", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Development/Chatbot-Template/trae/multi-ai-chatbot/frontend/src/src/App.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport './App.css';\n\nfunction App() {\n  const [input, setInput] = useState('');\n  const [provider, setProvider] = useState('mistral');\n  const [model, setModel] = useState(''); // New state for model selection\n  const [messages, setMessages] = useState([]);\n  const [response, setResponse] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const messagesEndRef = useRef(null);\n\n  const scrollToBottom = () => {\n    messagesEndRef.current?.scrollIntoView({ behavior: \"smooth\" });\n  };\n\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n\n  const handleSend = async () => {\n    if (!input.trim()) return;\n    const newMessages = [...messages, { role: 'user', content: input }];\n    setMessages(newMessages);\n    setInput('');\n    setLoading(true);\n    setResponse(null);\n    try {\n      const requestBody = { messages: newMessages, provider };\n      if (model) {\n        requestBody.model = model; // Add model to request body if selected\n      }\n      const res = await fetch('http://localhost:3001/chat', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify(requestBody),\n      });\n      const data = await res.json();\n      setResponse(data.result);\n      setMessages([...newMessages, { role: 'assistant', content: data.result?.choices?.[0]?.message?.content || data.result }]);\n    } catch (err) {\n      setResponse('Error: ' + err.message);\n    }\n    setInput('');\n    setLoading(false);\n  };\n\n  const handleProviderChange = (e) => {\n    const selectedProvider = e.target.value;\n    setProvider(selectedProvider);\n    // Set default model based on provider, or clear if not applicable\n    switch (selectedProvider) {\n      case 'mistral':\n        setModel('mistral-large-latest');\n        break;\n      case 'openrouter':\n        setModel('deepseek/deepseek-r1-0528:free'); // Default for OpenRouter\n        break;\n      case 'gemini':\n        setModel('gemini-2.0-flash'); // Default for Gemini\n        break;\n      default:\n        setModel('');\n    }\n  };\n\n  return (\n    <div className=\"App\">\n      <div className=\"chat-container\">\n        <div className=\"chat-header\">\n          <h1>AI Chatbot</h1>\n        </div>\n        <div className=\"provider-select-container\">\n          <label htmlFor=\"provider-select\">Choose a provider:</label>\n          <select id=\"provider-select\" value={provider} onChange={handleProviderChange}>\n            <option value=\"mistral\">Mistral</option>\n            <option value=\"openrouter\">OpenRouter</option>\n            <option value=\"gemini\">Gemini</option>\n          </select>\n          {(provider === 'openrouter' || provider === 'gemini') && (\n            <input\n              type=\"text\"\n              placeholder=\"Enter model (e.g., gpt-4, gemini-pro)\"\n              value={model}\n              onChange={(e) => setModel(e.target.value)}\n            />\n          )}\n        </div>\n        <div className=\"messages-container\">\n          {messages.map((msg, i) => (\n            <div key={i} className={`message ${msg.role}`}>\n              {msg.content}\n            </div>\n          ))}\n          {loading && <p className=\"loading-indicator\">Loading...</p>}\n          <div ref={messagesEndRef} />\n        </div>\n        <div className=\"input-area\">\n          <input\n            type=\"text\"\n            value={input}\n            onChange={e => setInput(e.target.value)}\n            onKeyDown={e => e.key === 'Enter' && handleSend()}\n            placeholder=\"Type your message...\"\n            disabled={loading}\n          />\n          <button onClick={handleSend} disabled={loading}>Send</button>\n        </div>\n      </div>\n    </div>\n  );\n}\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnB,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGR,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACS,QAAQ,EAAEC,WAAW,CAAC,GAAGV,QAAQ,CAAC,SAAS,CAAC;EACnD,MAAM,CAACW,KAAK,EAAEC,QAAQ,CAAC,GAAGZ,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EACxC,MAAM,CAACa,QAAQ,EAAEC,WAAW,CAAC,GAAGd,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACe,QAAQ,EAAEC,WAAW,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACiB,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAMmB,cAAc,GAAGjB,MAAM,CAAC,IAAI,CAAC;EAEnC,MAAMkB,cAAc,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA;IAC3B,CAAAA,qBAAA,GAAAF,cAAc,CAACG,OAAO,cAAAD,qBAAA,uBAAtBA,qBAAA,CAAwBE,cAAc,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EAChE,CAAC;EAEDvB,SAAS,CAAC,MAAM;IACdmB,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAACP,QAAQ,CAAC,CAAC;EAEd,MAAMY,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI,CAAClB,KAAK,CAACmB,IAAI,CAAC,CAAC,EAAE;IACnB,MAAMC,WAAW,GAAG,CAAC,GAAGd,QAAQ,EAAE;MAAEe,IAAI,EAAE,MAAM;MAAEC,OAAO,EAAEtB;IAAM,CAAC,CAAC;IACnEO,WAAW,CAACa,WAAW,CAAC;IACxBnB,QAAQ,CAAC,EAAE,CAAC;IACZU,UAAU,CAAC,IAAI,CAAC;IAChBF,WAAW,CAAC,IAAI,CAAC;IACjB,IAAI;MAAA,IAAAc,YAAA,EAAAC,oBAAA,EAAAC,qBAAA,EAAAC,sBAAA;MACF,MAAMC,WAAW,GAAG;QAAErB,QAAQ,EAAEc,WAAW;QAAElB;MAAS,CAAC;MACvD,IAAIE,KAAK,EAAE;QACTuB,WAAW,CAACvB,KAAK,GAAGA,KAAK,CAAC,CAAC;MAC7B;MACA,MAAMwB,GAAG,GAAG,MAAMC,KAAK,CAAC,4BAA4B,EAAE;QACpDC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UAAE,cAAc,EAAE;QAAmB,CAAC;QAC/CC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACP,WAAW;MAClC,CAAC,CAAC;MACF,MAAMQ,IAAI,GAAG,MAAMP,GAAG,CAACQ,IAAI,CAAC,CAAC;MAC7B3B,WAAW,CAAC0B,IAAI,CAACE,MAAM,CAAC;MACxB9B,WAAW,CAAC,CAAC,GAAGa,WAAW,EAAE;QAAEC,IAAI,EAAE,WAAW;QAAEC,OAAO,EAAE,EAAAC,YAAA,GAAAY,IAAI,CAACE,MAAM,cAAAd,YAAA,wBAAAC,oBAAA,GAAXD,YAAA,CAAae,OAAO,cAAAd,oBAAA,wBAAAC,qBAAA,GAApBD,oBAAA,CAAuB,CAAC,CAAC,cAAAC,qBAAA,wBAAAC,sBAAA,GAAzBD,qBAAA,CAA2Bc,OAAO,cAAAb,sBAAA,uBAAlCA,sBAAA,CAAoCJ,OAAO,KAAIa,IAAI,CAACE;MAAO,CAAC,CAAC,CAAC;IAC3H,CAAC,CAAC,OAAOG,GAAG,EAAE;MACZ/B,WAAW,CAAC,SAAS,GAAG+B,GAAG,CAACD,OAAO,CAAC;IACtC;IACAtC,QAAQ,CAAC,EAAE,CAAC;IACZU,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC;EAED,MAAM8B,oBAAoB,GAAIC,CAAC,IAAK;IAClC,MAAMC,gBAAgB,GAAGD,CAAC,CAACE,MAAM,CAACC,KAAK;IACvC1C,WAAW,CAACwC,gBAAgB,CAAC;IAC7B;IACA,QAAQA,gBAAgB;MACtB,KAAK,SAAS;QACZtC,QAAQ,CAAC,sBAAsB,CAAC;QAChC;MACF,KAAK,YAAY;QACfA,QAAQ,CAAC,gCAAgC,CAAC,CAAC,CAAC;QAC5C;MACF,KAAK,QAAQ;QACXA,QAAQ,CAAC,kBAAkB,CAAC,CAAC,CAAC;QAC9B;MACF;QACEA,QAAQ,CAAC,EAAE,CAAC;IAChB;EACF,CAAC;EAED,oBACER,OAAA;IAAKiD,SAAS,EAAC,KAAK;IAAAC,QAAA,eAClBlD,OAAA;MAAKiD,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7BlD,OAAA;QAAKiD,SAAS,EAAC,aAAa;QAAAC,QAAA,eAC1BlD,OAAA;UAAAkD,QAAA,EAAI;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB,CAAC,eACNtD,OAAA;QAAKiD,SAAS,EAAC,2BAA2B;QAAAC,QAAA,gBACxClD,OAAA;UAAOuD,OAAO,EAAC,iBAAiB;UAAAL,QAAA,EAAC;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC3DtD,OAAA;UAAQwD,EAAE,EAAC,iBAAiB;UAACR,KAAK,EAAE3C,QAAS;UAACoD,QAAQ,EAAEb,oBAAqB;UAAAM,QAAA,gBAC3ElD,OAAA;YAAQgD,KAAK,EAAC,SAAS;YAAAE,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACxCtD,OAAA;YAAQgD,KAAK,EAAC,YAAY;YAAAE,QAAA,EAAC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC9CtD,OAAA;YAAQgD,KAAK,EAAC,QAAQ;YAAAE,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC,EACR,CAACjD,QAAQ,KAAK,YAAY,IAAIA,QAAQ,KAAK,QAAQ,kBAClDL,OAAA;UACE0D,IAAI,EAAC,MAAM;UACXC,WAAW,EAAC,uCAAuC;UACnDX,KAAK,EAAEzC,KAAM;UACbkD,QAAQ,EAAGZ,CAAC,IAAKrC,QAAQ,CAACqC,CAAC,CAACE,MAAM,CAACC,KAAK;QAAE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C,CACF;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACNtD,OAAA;QAAKiD,SAAS,EAAC,oBAAoB;QAAAC,QAAA,GAChCzC,QAAQ,CAACmD,GAAG,CAAC,CAACC,GAAG,EAAEC,CAAC,kBACnB9D,OAAA;UAAaiD,SAAS,EAAE,WAAWY,GAAG,CAACrC,IAAI,EAAG;UAAA0B,QAAA,EAC3CW,GAAG,CAACpC;QAAO,GADJqC,CAAC;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEN,CACN,CAAC,EACDzC,OAAO,iBAAIb,OAAA;UAAGiD,SAAS,EAAC,mBAAmB;UAAAC,QAAA,EAAC;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC3DtD,OAAA;UAAK+D,GAAG,EAAEhD;QAAe;UAAAoC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB,CAAC,eACNtD,OAAA;QAAKiD,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBlD,OAAA;UACE0D,IAAI,EAAC,MAAM;UACXV,KAAK,EAAE7C,KAAM;UACbsD,QAAQ,EAAEZ,CAAC,IAAIzC,QAAQ,CAACyC,CAAC,CAACE,MAAM,CAACC,KAAK,CAAE;UACxCgB,SAAS,EAAEnB,CAAC,IAAIA,CAAC,CAACoB,GAAG,KAAK,OAAO,IAAI5C,UAAU,CAAC,CAAE;UAClDsC,WAAW,EAAC,sBAAsB;UAClCO,QAAQ,EAAErD;QAAQ;UAAAsC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC,eACFtD,OAAA;UAAQmE,OAAO,EAAE9C,UAAW;UAAC6C,QAAQ,EAAErD,OAAQ;UAAAqC,QAAA,EAAC;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACpD,EAAA,CA5GQD,GAAG;AAAAmE,EAAA,GAAHnE,GAAG;AA6GZ,eAAeA,GAAG;AAAC,IAAAmE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}