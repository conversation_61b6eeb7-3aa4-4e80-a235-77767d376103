{"version": 3, "file": "sdk.js", "sourceRoot": "", "sources": ["../src/sdk/sdk.ts"], "names": [], "mappings": ";AAAA;;GAEG;;;AAEH,4CAA2C;AAC3C,2CAAqC;AACrC,yCAAmC;AACnC,uCAAiC;AACjC,uCAAiC;AACjC,qDAA+C;AAC/C,mDAA6C;AAC7C,yCAAmC;AACnC,qCAA+B;AAC/B,mDAA6C;AAC7C,2CAAqC;AACrC,qCAA+B;AAE/B,MAAa,OAAQ,SAAQ,mBAAS;IAEpC,IAAI,MAAM;QACR,OAAO,CAAC,IAAI,CAAC,OAAO,KAAZ,IAAI,CAAC,OAAO,GAAK,IAAI,kBAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAC,CAAC;IACtD,CAAC;IAGD,IAAI,IAAI;QACN,OAAO,CAAC,IAAI,CAAC,KAAK,KAAV,IAAI,CAAC,KAAK,GAAK,IAAI,cAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAC,CAAC;IAClD,CAAC;IAGD,IAAI,KAAK;QACP,OAAO,CAAC,IAAI,CAAC,MAAM,KAAX,IAAI,CAAC,MAAM,GAAK,IAAI,gBAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAC,CAAC;IACpD,CAAC;IAGD,IAAI,UAAU;QACZ,OAAO,CAAC,IAAI,CAAC,WAAW,KAAhB,IAAI,CAAC,WAAW,GAAK,IAAI,0BAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAC,CAAC;IAC9D,CAAC;IAGD,IAAI,KAAK;QACP,OAAO,CAAC,IAAI,CAAC,MAAM,KAAX,IAAI,CAAC,MAAM,GAAK,IAAI,gBAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAC,CAAC;IACpD,CAAC;IAGD,IAAI,IAAI;QACN,OAAO,CAAC,IAAI,CAAC,KAAK,KAAV,IAAI,CAAC,KAAK,GAAK,IAAI,cAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAC,CAAC;IAClD,CAAC;IAGD,IAAI,GAAG;QACL,OAAO,CAAC,IAAI,CAAC,IAAI,KAAT,IAAI,CAAC,IAAI,GAAK,IAAI,YAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAC,CAAC;IAChD,CAAC;IAGD,IAAI,MAAM;QACR,OAAO,CAAC,IAAI,CAAC,OAAO,KAAZ,IAAI,CAAC,OAAO,GAAK,IAAI,kBAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAC,CAAC;IACtD,CAAC;IAGD,IAAI,UAAU;QACZ,OAAO,CAAC,IAAI,CAAC,WAAW,KAAhB,IAAI,CAAC,WAAW,GAAK,IAAI,0BAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAC,CAAC;IAC9D,CAAC;IAGD,IAAI,WAAW;QACb,OAAO,CAAC,IAAI,CAAC,YAAY,KAAjB,IAAI,CAAC,YAAY,GAAK,IAAI,4BAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAC,CAAC;IAChE,CAAC;IAGD,IAAI,GAAG;QACL,OAAO,CAAC,IAAI,CAAC,IAAI,KAAT,IAAI,CAAC,IAAI,GAAK,IAAI,YAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAC,CAAC;IAChD,CAAC;CACF;AAvDD,0BAuDC"}