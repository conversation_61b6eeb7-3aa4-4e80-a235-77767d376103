{"version": 3, "file": "sampletype.js", "sourceRoot": "", "sources": ["../../src/models/components/sampletype.ts"], "names": [], "mappings": ";AAAA;;GAEG;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,uCAAyB;AACzB,mDAI8B;AAEjB,QAAA,UAAU,GAAG;IACxB,QAAQ,EAAE,UAAU;IACpB,QAAQ,EAAE,UAAU;IACpB,YAAY,EAAE,eAAe;IAC7B,WAAW,EAAE,cAAc;IAC3B,UAAU,EAAE,aAAa;CACjB,CAAC;AAGX,gBAAgB;AACH,QAAA,wBAAwB,GAIjC,CAAC;KACF,KAAK,CAAC;IACL,CAAC,CAAC,UAAU,CAAC,kBAAU,CAAC;IACxB,CAAC,CAAC,MAAM,EAAE,CAAC,SAAS,CAAC,gCAAqB,CAAC;CAC5C,CAAC,CAAC;AAEL,gBAAgB;AACH,QAAA,yBAAyB,GAIlC,CAAC,CAAC,KAAK,CAAC;IACV,CAAC,CAAC,UAAU,CAAC,kBAAU,CAAC;IACxB,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,EAAwB,CAAC;CACjD,CAAC,CAAC;AAEH;;;GAGG;AACH,IAAiB,WAAW,CAK3B;AALD,WAAiB,WAAW;IAC1B,0DAA0D;IAC7C,yBAAa,GAAG,gCAAwB,CAAC;IACtD,2DAA2D;IAC9C,0BAAc,GAAG,iCAAyB,CAAC;AAC1D,CAAC,EALgB,WAAW,2BAAX,WAAW,QAK3B"}