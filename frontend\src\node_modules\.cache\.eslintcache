[{"C:\\Users\\<USER>\\Development\\Chatbot-Template\\trae\\multi-ai-chatbot\\frontend\\src\\src\\index.js": "1", "C:\\Users\\<USER>\\Development\\Chatbot-Template\\trae\\multi-ai-chatbot\\frontend\\src\\src\\App.js": "2", "C:\\Users\\<USER>\\Development\\Chatbot-Template\\trae\\multi-ai-chatbot\\frontend\\src\\src\\reportWebVitals.js": "3"}, {"size": 500, "mtime": 1750190009261, "results": "4", "hashOfConfig": "5"}, {"size": 3748, "mtime": 1749854331114, "results": "6", "hashOfConfig": "5"}, {"size": 362, "mtime": 1749846326629, "results": "7", "hashOfConfig": "5"}, {"filePath": "8", "messages": "9", "suppressedMessages": "10", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "xkcj5m", {"filePath": "11", "messages": "12", "suppressedMessages": "13", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "14", "messages": "15", "suppressedMessages": "16", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Development\\Chatbot-Template\\trae\\multi-ai-chatbot\\frontend\\src\\src\\index.js", [], [], "C:\\Users\\<USER>\\Development\\Chatbot-Template\\trae\\multi-ai-chatbot\\frontend\\src\\src\\App.js", ["17"], [], "C:\\Users\\<USER>\\Development\\Chatbot-Template\\trae\\multi-ai-chatbot\\frontend\\src\\src\\reportWebVitals.js", [], [], {"ruleId": "18", "severity": 1, "message": "19", "line": 9, "column": 10, "nodeType": "20", "messageId": "21", "endLine": 9, "endColumn": 18}, "no-unused-vars", "'response' is assigned a value but never used.", "Identifier", "unusedVar"]