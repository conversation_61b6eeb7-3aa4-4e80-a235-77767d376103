{"version": 3, "file": "ocrusageinfo.js", "sourceRoot": "", "sources": ["../../src/models/components/ocrusageinfo.ts"], "names": [], "mappings": ";AAAA;;GAEG;;;;;;;;;;;;;;;;;;;;;;;;;;AAoEH,gDAEC;AAED,oDAQC;AA9ED,uCAAyB;AACzB,2DAA0D;AAC1D,qDAAiD;AAejD,gBAAgB;AACH,QAAA,0BAA0B,GAInC,CAAC,CAAC,MAAM,CAAC;IACX,eAAe,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE;IACjC,cAAc,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,QAAQ,EAAE;CACxD,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE;IACjB,OAAO,IAAA,qBAAM,EAAC,CAAC,EAAE;QACf,iBAAiB,EAAE,gBAAgB;QACnC,gBAAgB,EAAE,cAAc;KACjC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAQH,gBAAgB;AACH,QAAA,2BAA2B,GAIpC,CAAC,CAAC,MAAM,CAAC;IACX,cAAc,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE;IAChC,YAAY,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,QAAQ,EAAE;CACtD,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE;IACjB,OAAO,IAAA,qBAAM,EAAC,CAAC,EAAE;QACf,cAAc,EAAE,iBAAiB;QACjC,YAAY,EAAE,gBAAgB;KAC/B,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH;;;GAGG;AACH,IAAiB,aAAa,CAO7B;AAPD,WAAiB,aAAa;IAC5B,4DAA4D;IAC/C,2BAAa,GAAG,kCAA0B,CAAC;IACxD,6DAA6D;IAChD,4BAAc,GAAG,mCAA2B,CAAC;AAG5D,CAAC,EAPgB,aAAa,6BAAb,aAAa,QAO7B;AAED,SAAgB,kBAAkB,CAAC,YAA0B;IAC3D,OAAO,IAAI,CAAC,SAAS,CAAC,mCAA2B,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC;AACzE,CAAC;AAED,SAAgB,oBAAoB,CAClC,UAAkB;IAElB,OAAO,IAAA,sBAAS,EACd,UAAU,EACV,CAAC,CAAC,EAAE,EAAE,CAAC,kCAA0B,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EACtD,0CAA0C,CAC3C,CAAC;AACJ,CAAC"}