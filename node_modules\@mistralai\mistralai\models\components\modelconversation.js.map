{"version": 3, "file": "modelconversation.js", "sourceRoot": "", "sources": ["../../src/models/components/modelconversation.ts"], "names": [], "mappings": ";AAAA;;GAEG;;;;;;;;;;;;;;;;;;;;;;;;;;AAyMH,oEAMC;AAED,wEAQC;AA6KD,0DAMC;AAED,8DAQC;AApZD,uCAAyB;AACzB,2DAA0D;AAC1D,qDAAiD;AAIjD,qEAKkC;AAClC,2DAK6B;AAC7B,qEAKkC;AAClC,uDAK2B;AAC3B,qEAKkC;AAClC,uEAKmC;AACnC,yDAK4B;AAUf,QAAA,uBAAuB,GAAG;IACrC,YAAY,EAAE,cAAc;CACpB,CAAC;AA0CX,gBAAgB;AACH,QAAA,oCAAoC,GAI7C,CAAC,CAAC,KAAK,CAAC;IACV,0DAAiC,CAAC,GAAG,CACnC,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,kBAAkB,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAClE,IAAI,EAAE,CAAC,CAAC,IAAI;KACb,CAAC,CAAC,CACJ;IACD,0DAAiC,CAAC,GAAG,CACnC,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,kBAAkB,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAClE,IAAI,EAAE,CAAC,CAAC,IAAI;KACb,CAAC,CAAC,CACJ;IACD,8CAA2B,CAAC,GAAG,CAC7B,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAC5D,IAAI,EAAE,CAAC,CAAC,IAAI;KACb,CAAC,CAAC,CACJ;IACD,4DAAkC,CAAC,GAAG,CACpC,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QACpE,IAAI,EAAE,CAAC,CAAC,IAAI;KACb,CAAC,CAAC,CACJ;IACD,0DAAiC,CAAC,GAAG,CACnC,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,kBAAkB,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAClE,IAAI,EAAE,CAAC,CAAC,IAAI;KACb,CAAC,CAAC,CACJ;IACD,4CAA0B,CAAC,GAAG,CAC5B,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAC1D,IAAI,EAAE,CAAC,CAAC,IAAI;KACb,CAAC,CAAC,CACJ;CACF,CAAC,CAAC;AAWH,gBAAgB;AACH,QAAA,qCAAqC,GAI9C,CAAC,CAAC,KAAK,CAAC;IACV,2DAAkC,CAAC,GAAG,CACpC,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,kBAAkB,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAClE,IAAI,EAAE,CAAC,CAAC,IAAI;KACb,CAAC,CAAC,CACJ;IACD,2DAAkC,CAAC,GAAG,CACpC,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,kBAAkB,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAClE,IAAI,EAAE,CAAC,CAAC,IAAI;KACb,CAAC,CAAC,CACJ;IACD,+CAA4B,CAAC,GAAG,CAC9B,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAC5D,IAAI,EAAE,CAAC,CAAC,IAAI;KACb,CAAC,CAAC,CACJ;IACD,6DAAmC,CAAC,GAAG,CACrC,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QACpE,IAAI,EAAE,CAAC,CAAC,IAAI;KACb,CAAC,CAAC,CACJ;IACD,2DAAkC,CAAC,GAAG,CACpC,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,kBAAkB,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAClE,IAAI,EAAE,CAAC,CAAC,IAAI;KACb,CAAC,CAAC,CACJ;IACD,6CAA2B,CAAC,GAAG,CAC7B,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAC1D,IAAI,EAAE,CAAC,CAAC,IAAI;KACb,CAAC,CAAC,CACJ;CACF,CAAC,CAAC;AAEH;;;GAGG;AACH,IAAiB,uBAAuB,CAOvC;AAPD,WAAiB,uBAAuB;IACtC,sEAAsE;IACzD,qCAAa,GAAG,4CAAoC,CAAC;IAClE,uEAAuE;IAC1D,sCAAc,GAAG,6CAAqC,CAAC;AAGtE,CAAC,EAPgB,uBAAuB,uCAAvB,uBAAuB,QAOvC;AAED,SAAgB,4BAA4B,CAC1C,sBAA8C;IAE9C,OAAO,IAAI,CAAC,SAAS,CACnB,6CAAqC,CAAC,KAAK,CAAC,sBAAsB,CAAC,CACpE,CAAC;AACJ,CAAC;AAED,SAAgB,8BAA8B,CAC5C,UAAkB;IAElB,OAAO,IAAA,sBAAS,EACd,UAAU,EACV,CAAC,CAAC,EAAE,EAAE,CAAC,4CAAoC,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAChE,oDAAoD,CACrD,CAAC;AACJ,CAAC;AAED,gBAAgB;AACH,QAAA,qCAAqC,GAE9C,CAAC,CAAC,UAAU,CAAC,+BAAuB,CAAC,CAAC;AAE1C,gBAAgB;AACH,QAAA,sCAAsC,GAE/C,6CAAqC,CAAC;AAE1C;;;GAGG;AACH,IAAiB,wBAAwB,CAKxC;AALD,WAAiB,wBAAwB;IACvC,uEAAuE;IAC1D,sCAAa,GAAG,6CAAqC,CAAC;IACnE,wEAAwE;IAC3D,uCAAc,GAAG,8CAAsC,CAAC;AACvE,CAAC,EALgB,wBAAwB,wCAAxB,wBAAwB,QAKxC;AAED,gBAAgB;AACH,QAAA,+BAA+B,GAIxC,CAAC,CAAC,MAAM,CAAC;IACX,YAAY,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,QAAQ,EAAE;IAC/C,KAAK,EAAE,CAAC,CAAC,KAAK,CACZ,CAAC,CAAC,KAAK,CAAC;QACN,0DAAiC,CAAC,GAAG,CACnC,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,kBAAkB,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;YAClE,IAAI,EAAE,CAAC,CAAC,IAAI;SACb,CAAC,CAAC,CACJ;QACD,0DAAiC,CAAC,GAAG,CACnC,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,kBAAkB,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;YAClE,IAAI,EAAE,CAAC,CAAC,IAAI;SACb,CAAC,CAAC,CACJ;QACD,8CAA2B,CAAC,GAAG,CAC7B,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;YAC5D,IAAI,EAAE,CAAC,CAAC,IAAI;SACb,CAAC,CAAC,CACJ;QACD,4DAAkC,CAAC,GAAG,CACpC,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;YACpE,IAAI,EAAE,CAAC,CAAC,IAAI;SACb,CAAC,CAAC,CACJ;QACD,0DAAiC,CAAC,GAAG,CACnC,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,kBAAkB,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;YAClE,IAAI,EAAE,CAAC,CAAC,IAAI;SACb,CAAC,CAAC,CACJ;QACD,4CAA0B,CAAC,GAAG,CAC5B,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;YAC1D,IAAI,EAAE,CAAC,CAAC,IAAI;SACb,CAAC,CAAC,CACJ;KACF,CAAC,CACH,CAAC,QAAQ,EAAE;IACZ,eAAe,EAAE,gDAA4B,CAAC,QAAQ,EAAE;IACxD,IAAI,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,QAAQ,EAAE;IACvC,WAAW,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,QAAQ,EAAE;IAC9C,MAAM,EAAE,6CAAqC,CAAC,OAAO,CAAC,cAAc,CAAC;IACrE,EAAE,EAAE,CAAC,CAAC,MAAM,EAAE;IACd,UAAU,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC;IAC7E,UAAU,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC;IAC7E,KAAK,EAAE,CAAC,CAAC,MAAM,EAAE;CAClB,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE;IACjB,OAAO,IAAA,qBAAM,EAAC,CAAC,EAAE;QACf,iBAAiB,EAAE,gBAAgB;QACnC,YAAY,EAAE,WAAW;QACzB,YAAY,EAAE,WAAW;KAC1B,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAyBH,gBAAgB;AACH,QAAA,gCAAgC,GAIzC,CAAC,CAAC,MAAM,CAAC;IACX,YAAY,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,QAAQ,EAAE;IAC/C,KAAK,EAAE,CAAC,CAAC,KAAK,CACZ,CAAC,CAAC,KAAK,CAAC;QACN,2DAAkC,CAAC,GAAG,CACpC,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,kBAAkB,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;YAClE,IAAI,EAAE,CAAC,CAAC,IAAI;SACb,CAAC,CAAC,CACJ;QACD,2DAAkC,CAAC,GAAG,CACpC,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,kBAAkB,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;YAClE,IAAI,EAAE,CAAC,CAAC,IAAI;SACb,CAAC,CAAC,CACJ;QACD,+CAA4B,CAAC,GAAG,CAC9B,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;YAC5D,IAAI,EAAE,CAAC,CAAC,IAAI;SACb,CAAC,CAAC,CACJ;QACD,6DAAmC,CAAC,GAAG,CACrC,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;YACpE,IAAI,EAAE,CAAC,CAAC,IAAI;SACb,CAAC,CAAC,CACJ;QACD,2DAAkC,CAAC,GAAG,CACpC,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,kBAAkB,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;YAClE,IAAI,EAAE,CAAC,CAAC,IAAI;SACb,CAAC,CAAC,CACJ;QACD,6CAA2B,CAAC,GAAG,CAC7B,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;YAC1D,IAAI,EAAE,CAAC,CAAC,IAAI;SACb,CAAC,CAAC,CACJ;KACF,CAAC,CACH,CAAC,QAAQ,EAAE;IACZ,cAAc,EAAE,iDAA6B,CAAC,QAAQ,EAAE;IACxD,IAAI,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,QAAQ,EAAE;IACvC,WAAW,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,QAAQ,EAAE;IAC9C,MAAM,EAAE,8CAAsC,CAAC,OAAO,CAAC,cAAc,CAAC;IACtE,EAAE,EAAE,CAAC,CAAC,MAAM,EAAE;IACd,SAAS,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;IACnD,SAAS,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;IACnD,KAAK,EAAE,CAAC,CAAC,MAAM,EAAE;CAClB,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE;IACjB,OAAO,IAAA,qBAAM,EAAC,CAAC,EAAE;QACf,cAAc,EAAE,iBAAiB;QACjC,SAAS,EAAE,YAAY;QACvB,SAAS,EAAE,YAAY;KACxB,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH;;;GAGG;AACH,IAAiB,kBAAkB,CAOlC;AAPD,WAAiB,kBAAkB;IACjC,iEAAiE;IACpD,gCAAa,GAAG,uCAA+B,CAAC;IAC7D,kEAAkE;IACrD,iCAAc,GAAG,wCAAgC,CAAC;AAGjE,CAAC,EAPgB,kBAAkB,kCAAlB,kBAAkB,QAOlC;AAED,SAAgB,uBAAuB,CACrC,iBAAoC;IAEpC,OAAO,IAAI,CAAC,SAAS,CACnB,wCAAgC,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAC1D,CAAC;AACJ,CAAC;AAED,SAAgB,yBAAyB,CACvC,UAAkB;IAElB,OAAO,IAAA,sBAAS,EACd,UAAU,EACV,CAAC,CAAC,EAAE,EAAE,CAAC,uCAA+B,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAC3D,+CAA+C,CAChD,CAAC;AACJ,CAAC"}