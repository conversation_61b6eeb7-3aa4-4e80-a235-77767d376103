{"version": 3, "file": "modelcapabilities.js", "sourceRoot": "", "sources": ["../../src/models/components/modelcapabilities.ts"], "names": [], "mappings": ";AAAA;;GAEG;;;;;;;;;;;;;;;;;;;;;;;;;;AA8EH,0DAMC;AAED,8DAQC;AA5FD,uCAAyB;AACzB,2DAA0D;AAC1D,qDAAiD;AAYjD,gBAAgB;AACH,QAAA,+BAA+B,GAIxC,CAAC,CAAC,MAAM,CAAC;IACX,eAAe,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC;IAC1C,cAAc,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC;IAC1C,gBAAgB,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC;IAC3C,WAAW,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC;IACvC,MAAM,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC;CACnC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE;IACjB,OAAO,IAAA,qBAAM,EAAC,CAAC,EAAE;QACf,iBAAiB,EAAE,gBAAgB;QACnC,gBAAgB,EAAE,eAAe;QACjC,kBAAkB,EAAE,iBAAiB;QACrC,aAAa,EAAE,YAAY;KAC5B,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAWH,gBAAgB;AACH,QAAA,gCAAgC,GAIzC,CAAC,CAAC,MAAM,CAAC;IACX,cAAc,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC;IACzC,aAAa,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC;IACzC,eAAe,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC;IAC1C,UAAU,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC;IACtC,MAAM,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC;CACnC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE;IACjB,OAAO,IAAA,qBAAM,EAAC,CAAC,EAAE;QACf,cAAc,EAAE,iBAAiB;QACjC,aAAa,EAAE,gBAAgB;QAC/B,eAAe,EAAE,kBAAkB;QACnC,UAAU,EAAE,aAAa;KAC1B,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH;;;GAGG;AACH,IAAiB,kBAAkB,CAOlC;AAPD,WAAiB,kBAAkB;IACjC,iEAAiE;IACpD,gCAAa,GAAG,uCAA+B,CAAC;IAC7D,kEAAkE;IACrD,iCAAc,GAAG,wCAAgC,CAAC;AAGjE,CAAC,EAPgB,kBAAkB,kCAAlB,kBAAkB,QAOlC;AAED,SAAgB,uBAAuB,CACrC,iBAAoC;IAEpC,OAAO,IAAI,CAAC,SAAS,CACnB,wCAAgC,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAC1D,CAAC;AACJ,CAAC;AAED,SAAgB,yBAAyB,CACvC,UAAkB;IAElB,OAAO,IAAA,sBAAS,EACd,UAAU,EACV,CAAC,CAAC,EAAE,EAAE,CAAC,uCAA+B,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAC3D,+CAA+C,CAChD,CAAC;AACJ,CAAC"}