{"version": 3, "file": "httpvalidationerror.js", "sourceRoot": "", "sources": ["../../src/models/errors/httpvalidationerror.ts"], "names": [], "mappings": ";AAAA;;GAEG;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,uCAAyB;AACzB,mEAAqD;AAMrD,MAAa,mBAAoB,SAAQ,KAAK;IAM5C,YAAY,GAA4B;QACtC,MAAM,OAAO,GAAG,SAAS,IAAI,GAAG,IAAI,OAAO,GAAG,CAAC,OAAO,KAAK,QAAQ;YACjE,CAAC,CAAC,GAAG,CAAC,OAAO;YACb,CAAC,CAAC,uBAAuB,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,CAAC;QACjD,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC;QAEjB,IAAI,GAAG,CAAC,MAAM,IAAI,IAAI;YAAE,IAAI,CAAC,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC;QAEjD,IAAI,CAAC,IAAI,GAAG,qBAAqB,CAAC;IACpC,CAAC;CACF;AAjBD,kDAiBC;AAED,gBAAgB;AACH,QAAA,iCAAiC,GAI1C,CAAC,CAAC,MAAM,CAAC;IACX,MAAM,EAAE,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC,6BAA6B,CAAC,CAAC,QAAQ,EAAE;CACrE,CAAC;KACC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE;IACf,OAAO,IAAI,mBAAmB,CAAC,CAAC,CAAC,CAAC;AACpC,CAAC,CAAC,CAAC;AAOL,gBAAgB;AACH,QAAA,kCAAkC,GAI3C,CAAC,CAAC,UAAU,CAAC,mBAAmB,CAAC;KAClC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC;KACvB,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC;IACb,MAAM,EAAE,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC,8BAA8B,CAAC,CAAC,QAAQ,EAAE;CACtE,CAAC,CAAC,CAAC;AAEN;;;GAGG;AACH,IAAiB,oBAAoB,CAOpC;AAPD,WAAiB,oBAAoB;IACnC,mEAAmE;IACtD,kCAAa,GAAG,yCAAiC,CAAC;IAC/D,oEAAoE;IACvD,mCAAc,GAAG,0CAAkC,CAAC;AAGnE,CAAC,EAPgB,oBAAoB,oCAApB,oBAAoB,QAOpC"}